import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";

export default function DebugAssetsPage() {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  useEffect(() => {
    addDebugInfo("Component mounted");
    
    // Test API connectivity
    const testAPI = async () => {
      try {
        addDebugInfo("Testing API connectivity...");
        const response = await fetch('http://localhost:5000/api/assets', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'
          }
        });
        
        addDebugInfo(`API Response Status: ${response.status}`);
        
        if (response.ok) {
          const data = await response.json();
          addDebugInfo(`API Response: ${JSON.stringify(data).substring(0, 100)}...`);
        } else {
          const errorText = await response.text();
          addDebugInfo(`API Error: ${errorText.substring(0, 100)}...`);
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Unknown error';
        addDebugInfo(`API Connection Error: ${errorMsg}`);
        setError(errorMsg);
      }
    };

    testAPI();
  }, []);

  return (
    <AppLayout>
      <div className="p-6">
        <div className="bg-white rounded-lg border p-6">
          <h1 className="text-2xl font-bold mb-4">Debug Assets Page</h1>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong>Error:</strong> {error}
            </div>
          )}
          
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">System Information</h2>
            <div className="bg-gray-50 p-4 rounded">
              <p><strong>Frontend URL:</strong> {window.location.origin}</p>
              <p><strong>API Base URL:</strong> http://localhost:5000/api</p>
              <p><strong>Current Time:</strong> {new Date().toLocaleString()}</p>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Debug Log</h2>
            <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto">
              {debugInfo.length === 0 ? (
                <p>No debug information yet...</p>
              ) : (
                debugInfo.map((info, index) => (
                  <div key={index}>{info}</div>
                ))
              )}
            </div>
          </div>

          <div className="flex gap-4">
            <button
              onClick={() => {
                setDebugInfo([]);
                setError(null);
                addDebugInfo("Debug log cleared");
              }}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Clear Log
            </button>
            
            <button
              onClick={() => {
                addDebugInfo("Manual test button clicked");
                alert("Debug page is working!");
              }}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Button
            </button>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
