// Model associations setup
import Asset from './Assets';
import AssetFile from './AssetFile';
import AssetType from './AssetType';
import AssetModel from './AssetModel';
import Vendor from './Vendors';
import Location from './masters/Location';

// Asset associations
Asset.belongsTo(AssetType, {
  foreignKey: 'assetTypeId',
  as: 'assetType'
});

Asset.belongsTo(AssetModel, {
  foreignKey: 'assetModelId',
  as: 'assetModel'
});

Asset.belongsTo(Vendor, {
  foreignKey: 'vendorId',
  as: 'vendor'
});

Asset.belongsTo(Location, {
  foreignKey: 'locationId',
  as: 'location'
});

// Asset File associations
Asset.hasMany(AssetFile, {
  foreignKey: 'assetId',
  as: 'files'
});

AssetFile.belongsTo(Asset, {
  foreignKey: 'assetId',
  as: 'asset'
});

// AssetType associations
AssetType.hasMany(Asset, {
  foreignKey: 'assetTypeId',
  as: 'assets'
});

AssetType.hasMany(AssetModel, {
  foreignKey: 'assetType',
  as: 'models'
});

// AssetModel associations
AssetModel.belongsTo(AssetType, {
  foreignKey: 'assetType',
  as: 'assetTypeDetails'
});

AssetModel.hasMany(Asset, {
  foreignKey: 'assetModelId',
  as: 'assets'
});

// Vendor associations
Vendor.hasMany(Asset, {
  foreignKey: 'vendorId',
  as: 'assets'
});

// Location associations
Location.hasMany(Asset, {
  foreignKey: 'locationId',
  as: 'assets'
});

export {
  Asset,
  AssetFile,
  AssetType,
  AssetModel,
  Vendor,
  Location
};
