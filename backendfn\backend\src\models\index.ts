// Model associations setup
import Asset from './Assets';
import AssetFile from './AssetFile';
import AssetType from './AssetType';
import AssetModel from './AssetModel';
import Vendor from './Vendors';
import Location from './masters/Location';

// Asset associations
// Note: Asset model now uses simple string fields for category and location
// Only vendor relationship is maintained as foreign key

Asset.belongsTo(Vendor, {
  foreignKey: 'vendorId',
  as: 'vendor'
});

// Asset File associations
Asset.hasMany(AssetFile, {
  foreignKey: 'assetId',
  as: 'files'
});

AssetFile.belongsTo(Asset, {
  foreignKey: 'assetId',
  as: 'asset'
});

// Vendor associations
Vendor.hasMany(Asset, {
  foreignKey: 'vendorId',
  as: 'assets'
});

export {
  Asset,
  AssetFile,
  AssetType,
  AssetModel,
  Vendor,
  Location
};
