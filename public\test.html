<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic HTML Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: white;
            color: black;
        }
        .test-box {
            background-color: yellow;
            padding: 20px;
            border: 3px solid red;
            margin: 20px 0;
        }
        .success {
            background-color: lightgreen;
            padding: 15px;
            border: 2px solid green;
        }
    </style>
</head>
<body>
    <h1 style="color: red; font-size: 48px;">BASIC HTML TEST</h1>
    
    <div class="test-box">
        <h2>If you can see this yellow box, HTML is working!</h2>
        <p>This is a simple HTML file served directly.</p>
    </div>
    
    <div class="success">
        <h3>✅ HTML is loading</h3>
        <h3>✅ CSS is working</h3>
        <h3>✅ Browser is functioning</h3>
    </div>
    
    <button onclick="testJS()" style="background: blue; color: white; padding: 15px 30px; font-size: 18px; border: none; border-radius: 5px; cursor: pointer;">
        Test JavaScript
    </button>
    
    <div id="js-result" style="margin-top: 20px; padding: 10px; background: #f0f0f0;"></div>
    
    <script>
        console.log('JavaScript is working!');
        
        function testJS() {
            document.getElementById('js-result').innerHTML = 
                '<h3 style="color: green;">✅ JavaScript is working! Time: ' + new Date().toLocaleString() + '</h3>';
            alert('JavaScript test successful!');
        }
        
        // Auto-run test
        window.onload = function() {
            document.getElementById('js-result').innerHTML = 
                '<h3 style="color: blue;">JavaScript loaded automatically at: ' + new Date().toLocaleString() + '</h3>';
        };
    </script>
</body>
</html>
