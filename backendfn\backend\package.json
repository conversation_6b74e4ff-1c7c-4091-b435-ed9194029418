{"name": "rfi-asset-management-backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "create-tables": "npx ts-node src/scripts/create-tables.ts", "test-db": "npx ts-node src/scripts/test-connection.ts", "setup-db": "npx ts-node src/scripts/setup-database.ts", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["asset-management", "rfi", "mysql", "express"], "author": "RFI Asset Management Team", "license": "ISC", "description": "Backend API for RFI Asset Management System", "dependencies": {"@types/multer": "^1.4.13", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/node": "^22.15.30", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "sequelize-cli": "^6.6.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}