import React from 'react';

function SimpleApp() {
  console.log('SimpleApp is rendering');
  
  return (
    <div style={{
      padding: '20px',
      backgroundColor: 'white',
      color: 'black',
      fontSize: '24px',
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: 'red', fontSize: '48px' }}>
        SIMPLE APP TEST - CAN YOU SEE THIS?
      </h1>
      
      <div style={{ backgroundColor: 'yellow', padding: '20px', margin: '20px 0' }}>
        <h2>If you can see this yellow box, React is working!</h2>
        <p>Time: {new Date().toLocaleString()}</p>
      </div>
      
      <div style={{ backgroundColor: 'lightblue', padding: '20px' }}>
        <h3>Debug Info:</h3>
        <ul>
          <li>✅ HTML is loading</li>
          <li>✅ JavaScript is executing</li>
          <li>✅ React is rendering</li>
          <li>✅ Styles are working</li>
        </ul>
      </div>
      
      <button 
        onClick={() => {
          alert('Button works! JavaScript is functional.');
          console.log('Button clicked successfully');
        }}
        style={{
          backgroundColor: 'green',
          color: 'white',
          padding: '15px 30px',
          fontSize: '18px',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          marginTop: '20px'
        }}
      >
        Click Me to Test JavaScript
      </button>
    </div>
  );
}

export default SimpleApp;
