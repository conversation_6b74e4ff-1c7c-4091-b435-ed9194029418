import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Eye, 
  Trash2, 
  FileText, 
  Image, 
  File,
  Loader2,
  Upload
} from 'lucide-react';
import { AssetFile } from '@/services/assetService';
import assetService from '@/services/assetService';
import { FileUpload } from './FileUpload';

interface FileListProps {
  assetId: number;
  assetName: string;
  onClose: () => void;
}

interface FileWithPreview extends File {
  preview?: string;
  description?: string;
}

export const FileList: React.FC<FileListProps> = ({
  assetId,
  assetName,
  onClose
}) => {
  const [files, setFiles] = useState<AssetFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([]);

  useEffect(() => {
    loadFiles();
  }, [assetId]);

  const loadFiles = async () => {
    try {
      setLoading(true);
      const response = await assetService.getAssetFiles(assetId);
      if (response.success) {
        setFiles(response.data || []);
      }
    } catch (error) {
      console.error('Error loading files:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setUploading(true);
      const descriptions = selectedFiles.map(f => f.description || '');
      const response = await assetService.uploadFiles(
        assetId, 
        selectedFiles, 
        'Current User', // TODO: Get from auth context
        descriptions
      );

      if (response.success) {
        setSelectedFiles([]);
        setShowUpload(false);
        await loadFiles(); // Reload files list
      }
    } catch (error) {
      console.error('Error uploading files:', error);
    } finally {
      setUploading(false);
    }
  };

  const handleDownload = async (file: AssetFile) => {
    try {
      const blob = await assetService.downloadFile(file.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const handleView = async (file: AssetFile) => {
    try {
      const blob = await assetService.downloadFile(file.id);
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error viewing file:', error);
    }
  };

  const handleDelete = async (file: AssetFile) => {
    if (!confirm(`Are you sure you want to delete "${file.fileName}"?`)) {
      return;
    }

    try {
      await assetService.deleteFile(file.id);
      await loadFiles(); // Reload files list
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="h-6 w-6 text-blue-500" />;
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-6 w-6 text-red-500" />;
    } else {
      return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Files for {assetName}</h2>
          <p className="text-muted-foreground">Asset ID: #{assetId}</p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setShowUpload(!showUpload)}
            disabled={uploading}
          >
            <Upload className="mr-2 h-4 w-4" />
            Upload Files
          </Button>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>

      {/* File Upload Section */}
      {showUpload && (
        <Card>
          <CardHeader>
            <CardTitle>Upload New Files</CardTitle>
          </CardHeader>
          <CardContent>
            <FileUpload
              onFilesSelected={setSelectedFiles}
              isUploading={uploading}
              maxFiles={10}
              maxSize={10 * 1024 * 1024} // 10MB
              acceptedTypes={['image/*', 'application/pdf']}
            />
            
            {selectedFiles.length > 0 && (
              <div className="flex justify-end space-x-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedFiles([]);
                    setShowUpload(false);
                  }}
                  disabled={uploading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpload}
                  disabled={uploading || selectedFiles.length === 0}
                >
                  {uploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Upload {selectedFiles.length} File{selectedFiles.length !== 1 ? 's' : ''}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Files List */}
      <Card>
        <CardHeader>
          <CardTitle>Attached Files ({files.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading files...</span>
            </div>
          ) : files.length === 0 ? (
            <div className="text-center py-8">
              <File className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-lg text-gray-500">No files attached</p>
              <p className="text-sm text-gray-400">Upload files to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {files.map((file) => (
                <div key={file.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                  {/* File Icon */}
                  <div className="flex-shrink-0">
                    {getFileIcon(file.fileType)}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <p className="font-medium truncate">{file.fileName}</p>
                      <Badge variant="secondary" className="text-xs">
                        {file.fileType.split('/')[1]?.toUpperCase() || 'FILE'}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{formatFileSize(file.fileSize)}</span>
                      <span>•</span>
                      <span>Uploaded {formatDate(file.uploadDate)}</span>
                      {file.uploadedBy && (
                        <>
                          <span>•</span>
                          <span>by {file.uploadedBy}</span>
                        </>
                      )}
                    </div>
                    {file.description && (
                      <p className="text-sm text-gray-600 mt-1">{file.description}</p>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleView(file)}
                      title="View file"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownload(file)}
                      title="Download file"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(file)}
                      title="Delete file"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
