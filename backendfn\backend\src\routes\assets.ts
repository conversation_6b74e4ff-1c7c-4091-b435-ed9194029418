import { Router, Request, Response } from 'express';
import multer from 'multer';
import { Asset, AssetFile, AssetType, AssetModel, Vendor, Location } from '../models';
import { AssetAttributes, AssetCreationAttributes } from '../models/Assets';
import { serialNumberService } from '../services/serialNumberService';
import { Op } from 'sequelize';

const router = Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req: any, file: any, cb: any) => {
    // Allow only PDF and image files
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF and image files are allowed.'));
    }
  },
});

// GET /api/assets - Get all assets with optional filtering
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 50,
      search,
      assetTypeId,
      status,
      condition,
      locationId,
      vendorId,
      county,
      precinct
    } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    const whereClause: any = {};

    // Add filters
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { serialNumber: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (assetTypeId) whereClause.assetTypeId = assetTypeId;
    if (status) whereClause.status = status;
    if (condition) whereClause.condition = condition;
    if (locationId) whereClause.locationId = locationId;
    if (vendorId) whereClause.vendorId = vendorId;
    if (county) whereClause.county = county;
    if (precinct) whereClause.precinct = precinct;

    const { count, rows } = await Asset.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: AssetType,
          as: 'assetType',
          attributes: ['id', 'name']
        },
        {
          model: AssetModel,
          as: 'assetModel',
          attributes: ['id', 'modelName', 'manufacturer'],
          required: false
        },
        {
          model: Vendor,
          as: 'vendor',
          attributes: ['id', 'companyName'],
          required: false
        },
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      limit: Number(limit),
      offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        assets: rows,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count,
          pages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching assets:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assets'
    });
  }
});

// GET /api/assets/stats - Get asset statistics
router.get('/stats', async (req: Request, res: Response): Promise<void> => {
  try {
    const totalAssets = await Asset.count();
    const availableAssets = await Asset.count({ where: { status: 'Available' } });
    const inTransitAssets = await Asset.count({ where: { status: 'In Transit' } });
    const deployedAssets = await Asset.count({ where: { status: 'Deployed' } });
    const damagedAssets = await Asset.count({ where: { status: 'Damaged' } });
    const underRepairAssets = await Asset.count({ where: { status: 'Under Repair' } });

    // Asset types breakdown
    const assetTypes = await Asset.findAll({
      attributes: [
        'type',
        [Asset.sequelize!.fn('COUNT', Asset.sequelize!.col('id')), 'count']
      ],
      group: ['type'],
      raw: true
    });

    // Condition breakdown
    const conditionStats = await Asset.findAll({
      attributes: [
        'condition',
        [Asset.sequelize!.fn('COUNT', Asset.sequelize!.col('id')), 'count']
      ],
      group: ['condition'],
      raw: true
    });

    res.json({
      total: totalAssets,
      available: availableAssets,
      inTransit: inTransitAssets,
      deployed: deployedAssets,
      damaged: damagedAssets,
      underRepair: underRepairAssets,
      assetTypes,
      conditionStats
    });
  } catch (error) {
    console.error('Error fetching asset stats:', error);
    res.status(500).json({ error: 'Failed to fetch asset statistics' });
  }
});

// GET /api/assets/:id - Get specific asset
router.get('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const asset = await Asset.findByPk(req.params.id);
    if (!asset) {
      res.status(404).json({ error: 'Asset not found' });
      return;
    }
    res.json(asset);
  } catch (error) {
    console.error('Error fetching asset:', error);
    res.status(500).json({ error: 'Failed to fetch asset' });
  }
});

// GET /api/assets/by-asset-id/:assetId - Get asset by assetId
router.get('/by-asset-id/:assetId', async (req: Request, res: Response): Promise<void> => {
  try {
    const asset = await Asset.findOne({ where: { id: req.params.assetId } });
    if (!asset) {
      res.status(404).json({ error: 'Asset not found' });
      return;
    }
    res.json(asset);
  } catch (error) {
    console.error('Error fetching asset:', error);
    res.status(500).json({ error: 'Failed to fetch asset' });
  }
});

// POST /api/assets - Create new asset
router.post('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const assetData: AssetCreationAttributes = req.body;

    // Validate required fields
    if (!assetData.name || !assetData.assetTypeId) {
      res.status(400).json({
        success: false,
        error: 'Asset name and asset type are required'
      });
      return;
    }

    // Generate serial number if not provided
    if (!assetData.serialNumber && assetData.assetTypeId) {
      assetData.serialNumber = await serialNumberService.generateSerialNumber(assetData.assetTypeId);
    }

    // Check if serial number already exists
    if (assetData.serialNumber) {
      const existingAsset = await Asset.findOne({ where: { serialNumber: assetData.serialNumber } });
      if (existingAsset) {
        res.status(400).json({
          success: false,
          error: 'Serial number already exists'
        });
        return;
      }
    }

    const asset = await Asset.create(assetData);

    // Fetch the created asset with associations
    const createdAsset = await Asset.findByPk(asset.id, {
      include: [
        {
          model: AssetType,
          as: 'assetType',
          attributes: ['id', 'name']
        },
        {
          model: AssetModel,
          as: 'assetModel',
          attributes: ['id', 'modelName', 'manufacturer'],
          required: false
        },
        {
          model: Vendor,
          as: 'vendor',
          attributes: ['id', 'companyName'],
          required: false
        },
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name'],
          required: false
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Asset created successfully',
      data: { asset: createdAsset }
    });
  } catch (error) {
    console.error('Error creating asset:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create asset'
    });
  }
});

// PUT /api/assets/:id - Update asset
router.put('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const asset = await Asset.findByPk(req.params.id);
    if (!asset) {
      res.status(404).json({ error: 'Asset not found' });
      return;
    }

    // Check if assetId is being changed and if it already exists
    if (req.body.id && req.body.id !== asset.id) {
      const existingAsset = await Asset.findOne({ where: { id: req.body.id } });
      if (existingAsset) {
        res.status(400).json({ error: 'Asset ID already exists' });
        return;
      }
    }

    await asset.update(req.body);
    res.json(asset);
  } catch (error) {
    console.error('Error updating asset:', error);
    res.status(500).json({ error: 'Failed to update asset' });
  }
});

// DELETE /api/assets/:id - Delete asset
router.delete('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const asset = await Asset.findByPk(req.params.id);
    if (!asset) {
      res.status(404).json({ error: 'Asset not found' });
      return;
    }

    await asset.destroy();
    res.json({ message: 'Asset deleted successfully' });
  } catch (error) {
    console.error('Error deleting asset:', error);
    res.status(500).json({ error: 'Failed to delete asset' });
  }
});

// PUT /api/assets/:id/status - Update asset status
router.put('/:id/status', async (req: Request, res: Response): Promise<void> => {
  try {
    const { status, location, assignedTo, notes } = req.body;
    
    const asset = await Asset.findByPk(req.params.id);
    if (!asset) {
      res.status(404).json({ error: 'Asset not found' });
      return;
    }

    const updateData: any = { status, lastChecked: new Date() };
    if (location) updateData.location = location;
    if (assignedTo !== undefined) updateData.assignedTo = assignedTo;
    if (notes !== undefined) updateData.notes = notes;

    await asset.update(updateData);
    res.json(asset);
  } catch (error) {
    console.error('Error updating asset status:', error);
    res.status(500).json({ error: 'Failed to update asset status' });
  }
});

// PUT /api/assets/:id/condition - Update asset condition
router.put('/:id/condition', async (req: Request, res: Response): Promise<void> => {
  try {
    const { condition, notes } = req.body;
    
    const asset = await Asset.findByPk(req.params.id);
    if (!asset) {
      res.status(404).json({ error: 'Asset not found' });
      return;
    }

    await asset.update({
      condition,
      notes: notes || asset.notes,
      lastChecked: new Date()
    });

    res.json(asset);
  } catch (error) {
    console.error('Error updating asset condition:', error);
    res.status(500).json({ error: 'Failed to update asset condition' });
  }
});

// GET /api/assets/locations/list - Get unique locations
router.get('/locations/list', async (req: Request, res: Response): Promise<void> => {
  try {
    const locations = await Asset.findAll({
      attributes: ['location'],
      group: ['location'],
      order: [['location', 'ASC']],
      raw: true
    });

    res.json(locations.map(l => l.name));
  } catch (error) {
    console.error('Error fetching locations:', error);
    res.status(500).json({ error: 'Failed to fetch locations' });
  }
});

// GET /api/assets/types/list - Get unique asset types
router.get('/types/list', async (req: Request, res: Response): Promise<void> => {
  try {
    const types = await Asset.findAll({
      attributes: ['type'],
      group: ['type'],
      order: [['type', 'ASC']],
      raw: true
    });

    res.json(types.map(t => t.name));
  } catch (error) {
    console.error('Error fetching asset types:', error);
    res.status(500).json({ error: 'Failed to fetch asset types' });
  }
});

// GET /api/assets/dropdown/asset-types - Get asset types for dropdown
router.get('/dropdown/asset-types', async (req: Request, res: Response): Promise<void> => {
  try {
    const assetTypes = await AssetType.findAll({
      where: { status: true },
      attributes: ['id', 'name'],
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: assetTypes
    });
  } catch (error) {
    console.error('Error fetching asset types:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch asset types'
    });
  }
});

// GET /api/assets/dropdown/asset-models/:categoryId - Get asset models filtered by category
router.get('/dropdown/asset-models/:categoryId', async (req: Request, res: Response): Promise<void> => {
  try {
    const { categoryId } = req.params;

    const assetModels = await AssetModel.findAll({
      where: {
        assetType: categoryId,
        status: true
      },
      attributes: ['id', 'modelName', 'manufacturer'],
      order: [['modelName', 'ASC']]
    });

    res.json({
      success: true,
      data: assetModels
    });
  } catch (error) {
    console.error('Error fetching asset models:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch asset models'
    });
  }
});

// GET /api/assets/dropdown/vendors - Get vendors for dropdown
router.get('/dropdown/vendors', async (req: Request, res: Response): Promise<void> => {
  try {
    const vendors = await Vendor.findAll({
      where: { status: true },
      attributes: ['id', 'companyName', 'firstName', 'lastName'],
      order: [['companyName', 'ASC']]
    });

    res.json({
      success: true,
      data: vendors
    });
  } catch (error) {
    console.error('Error fetching vendors:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch vendors'
    });
  }
});

// GET /api/assets/dropdown/locations - Get locations for dropdown
router.get('/dropdown/locations', async (req: Request, res: Response): Promise<void> => {
  try {
    const locations = await Location.findAll({
      where: { status: true },
      attributes: ['id', 'name', 'area', 'city'],
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: locations
    });
  } catch (error) {
    console.error('Error fetching locations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch locations'
    });
  }
});

// GET /api/assets/serial-number/next/:categoryId - Get next serial number for category
router.get('/serial-number/next/:categoryId', async (req: Request, res: Response): Promise<void> => {
  try {
    const { categoryId } = req.params;

    const nextSerialNumber = await serialNumberService.getNextSerialNumber(categoryId);

    res.json({
      success: true,
      data: { serialNumber: nextSerialNumber }
    });
  } catch (error) {
    console.error('Error generating serial number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate serial number'
    });
  }
});

// GET /api/assets/:id/files - Get files for an asset
router.get('/:id/files', async (req: Request, res: Response): Promise<void> => {
  try {
    const assetId = parseInt(req.params.id);

    // Check if asset exists
    const asset = await Asset.findByPk(assetId);
    if (!asset) {
      res.status(404).json({
        success: false,
        error: 'Asset not found'
      });
      return;
    }

    const files = await AssetFile.findAll({
      where: {
        assetId,
        isActive: true
      },
      attributes: ['id', 'fileName', 'fileType', 'fileSize', 'uploadDate', 'uploadedBy', 'description'],
      order: [['uploadDate', 'DESC']]
    });

    res.json({
      success: true,
      data: files
    });
  } catch (error) {
    console.error('Error fetching asset files:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch asset files'
    });
  }
});

// POST /api/assets/:id/files - Upload files to an asset
router.post('/:id/files', upload.array('files', 10), async (req: Request, res: Response): Promise<void> => {
  try {
    const assetId = parseInt(req.params.id);
    const files = req.files as any[];
    const { uploadedBy, descriptions } = req.body;

    // Check if asset exists
    const asset = await Asset.findByPk(assetId);
    if (!asset) {
      res.status(404).json({
        success: false,
        error: 'Asset not found'
      });
      return;
    }

    if (!files || files.length === 0) {
      res.status(400).json({
        success: false,
        error: 'No files provided'
      });
      return;
    }

    const uploadedFiles = [];
    const descriptionsArray = Array.isArray(descriptions) ? descriptions : [descriptions];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const description = descriptionsArray[i] || '';

      const assetFile = await AssetFile.create({
        assetId,
        fileName: file.originalname,
        fileType: file.mimetype,
        fileSize: file.size,
        fileData: file.buffer,
        uploadedBy,
        description
      });

      uploadedFiles.push({
        id: assetFile.id,
        fileName: assetFile.fileName,
        fileType: assetFile.fileType,
        fileSize: assetFile.fileSize,
        uploadDate: assetFile.uploadDate,
        uploadedBy: assetFile.uploadedBy,
        description: assetFile.description
      });
    }

    res.status(201).json({
      success: true,
      message: `Successfully uploaded ${uploadedFiles.length} file(s)`,
      data: uploadedFiles
    });
  } catch (error) {
    console.error('Error uploading files:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload files'
    });
  }
});

// GET /api/assets/files/:fileId/download - Download a specific file
router.get('/files/:fileId/download', async (req: Request, res: Response): Promise<void> => {
  try {
    const fileId = parseInt(req.params.fileId);

    const assetFile = await AssetFile.findOne({
      where: {
        id: fileId,
        isActive: true
      }
    });

    if (!assetFile) {
      res.status(404).json({
        success: false,
        error: 'File not found'
      });
      return;
    }

    // Set appropriate headers for file download
    res.setHeader('Content-Type', assetFile.fileType);
    res.setHeader('Content-Disposition', `attachment; filename="${assetFile.fileName}"`);
    res.setHeader('Content-Length', assetFile.fileSize.toString());

    // Send the file data
    res.send(assetFile.fileData);
  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download file'
    });
  }
});

// DELETE /api/assets/files/:fileId - Delete a file (soft delete)
router.delete('/files/:fileId', async (req: Request, res: Response): Promise<void> => {
  try {
    const fileId = parseInt(req.params.fileId);

    const assetFile = await AssetFile.findByPk(fileId);
    if (!assetFile) {
      res.status(404).json({
        success: false,
        error: 'File not found'
      });
      return;
    }

    await assetFile.update({ isActive: false });

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete file'
    });
  }
});

export default router;