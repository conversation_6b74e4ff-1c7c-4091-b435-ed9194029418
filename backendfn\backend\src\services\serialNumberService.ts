import Asset from '../models/Assets';
import AssetType from '../models/AssetType';
import { Op } from 'sequelize';

export interface SerialNumberConfig {
  categoryId: string;
  prefix?: string;
  length?: number;
  startNumber?: number;
}

class SerialNumberService {
  /**
   * Generate a unique serial number for an asset based on its category
   * Format: [CATEGORY_PREFIX][INCREMENT] (e.g., CAT001, CAT002)
   */
  async generateSerialNumber(categoryId: string, customConfig?: Partial<SerialNumberConfig>): Promise<string> {
    try {
      // Get the asset type to determine the prefix
      const assetType = await AssetType.findByPk(categoryId);
      if (!assetType) {
        throw new Error(`Asset type with ID ${categoryId} not found`);
      }

      // Generate prefix from asset type name or use custom prefix
      const prefix = customConfig?.prefix || this.generatePrefix(assetType.name);
      const length = customConfig?.length || 3; // Default to 3 digits
      const startNumber = customConfig?.startNumber || 1;

      // Find the highest existing serial number for this category
      const existingAssets = await Asset.findAll({
        where: {
          category: categoryId,
          serialNumber: {
            [Op.like]: `${prefix}%`
          }
        },
        order: [['serialNumber', 'DESC']],
        limit: 1
      });

      let nextNumber = startNumber;

      if (existingAssets.length > 0) {
        const lastSerialNumber = existingAssets[0].serialNumber;
        if (lastSerialNumber) {
          // Extract the numeric part from the last serial number
          const numericPart = lastSerialNumber.replace(prefix, '');
          const lastNumber = parseInt(numericPart, 10);
          
          if (!isNaN(lastNumber)) {
            nextNumber = lastNumber + 1;
          }
        }
      }

      // Format the number with leading zeros
      const formattedNumber = nextNumber.toString().padStart(length, '0');
      const serialNumber = `${prefix}${formattedNumber}`;

      // Ensure uniqueness by checking if this serial number already exists
      const existingAsset = await Asset.findOne({
        where: { serialNumber }
      });

      if (existingAsset) {
        // If it exists, recursively try the next number
        return this.generateSerialNumber(categoryId, {
          ...customConfig,
          startNumber: nextNumber + 1
        });
      }

      return serialNumber;
    } catch (error) {
      console.error('Error generating serial number:', error);
      throw new Error('Failed to generate serial number');
    }
  }

  /**
   * Generate a prefix from asset type name
   * Takes the first 3 characters of each word, up to 6 characters total
   */
  private generatePrefix(assetTypeName: string): string {
    const words = assetTypeName.trim().toUpperCase().split(/\s+/);
    
    if (words.length === 1) {
      // Single word: take first 3 characters
      return words[0].substring(0, 3);
    } else if (words.length === 2) {
      // Two words: take first 3 characters of each
      return words[0].substring(0, 3) + words[1].substring(0, 3);
    } else {
      // Multiple words: take first 2 characters of first 3 words
      return words.slice(0, 3).map(word => word.substring(0, 2)).join('');
    }
  }

  /**
   * Validate if a serial number follows the expected format for a category
   */
  async validateSerialNumber(serialNumber: string, categoryId: string): Promise<boolean> {
    try {
      const assetType = await AssetType.findByPk(categoryId);
      if (!assetType) {
        return false;
      }

      const expectedPrefix = this.generatePrefix(assetType.name);
      return serialNumber.startsWith(expectedPrefix);
    } catch (error) {
      console.error('Error validating serial number:', error);
      return false;
    }
  }

  /**
   * Get the next available serial number for a category without creating it
   */
  async getNextSerialNumber(categoryId: string): Promise<string> {
    return this.generateSerialNumber(categoryId);
  }

  /**
   * Check if a serial number is available (not already used)
   */
  async isSerialNumberAvailable(serialNumber: string): Promise<boolean> {
    try {
      const existingAsset = await Asset.findOne({
        where: { serialNumber }
      });
      return !existingAsset;
    } catch (error) {
      console.error('Error checking serial number availability:', error);
      return false;
    }
  }

  /**
   * Get serial number statistics for a category
   */
  async getSerialNumberStats(categoryId: string): Promise<{
    totalCount: number;
    lastSerialNumber: string | null;
    nextSerialNumber: string;
    prefix: string;
  }> {
    try {
      const assetType = await AssetType.findByPk(categoryId);
      if (!assetType) {
        throw new Error(`Asset type with ID ${categoryId} not found`);
      }

      const prefix = this.generatePrefix(assetType.name);
      
      const assets = await Asset.findAll({
        where: {
          category: categoryId,
          serialNumber: {
            [Op.like]: `${prefix}%`
          }
        },
        order: [['serialNumber', 'DESC']]
      });

      const totalCount = assets.length;
      const lastSerialNumber = assets.length > 0 ? (assets[0].serialNumber || null) : null;
      const nextSerialNumber = await this.getNextSerialNumber(categoryId);

      return {
        totalCount,
        lastSerialNumber,
        nextSerialNumber,
        prefix
      };
    } catch (error) {
      console.error('Error getting serial number stats:', error);
      throw new Error('Failed to get serial number statistics');
    }
  }
}

export const serialNumberService = new SerialNumberService();
export default serialNumberService;
