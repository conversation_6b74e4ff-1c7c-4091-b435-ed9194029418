import React, { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { SimpleAssetDataGrid } from "@/components/assets/SimpleAssetDataGrid";

export default function SimpleAssetsPage() {
  const [viewMode, setViewMode] = useState<'status' | 'grid'>('status');

  const handleCreateAsset = () => {
    alert('Create Asset clicked!');
  };

  const handleEditAsset = (asset: any) => {
    alert(`Edit Asset: ${asset.name}`);
  };

  const handleDeleteAsset = (asset: any) => {
    alert(`Delete Asset: ${asset.name}`);
  };

  const handleViewFiles = (asset: any) => {
    alert(`View Files for: ${asset.name}`);
  };

  return (
    <AppLayout>
      <div className="p-6">
        <div className="mb-4">
          <button
            onClick={() => setViewMode('status')}
            className={`mr-2 px-4 py-2 rounded ${viewMode === 'status' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            Status View
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`px-4 py-2 rounded ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            Grid View
          </button>
        </div>

        {viewMode === 'status' ? (
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
            <h1 style={{ color: 'black', fontSize: '24px', marginBottom: '20px' }}>
              Assets Management - Status
            </h1>
            <p style={{ color: 'black', fontSize: '16px', marginBottom: '20px' }}>
              This is a simplified version of the Assets page to test if the layout is working.
            </p>
            <div style={{
              backgroundColor: '#f9fafb',
              padding: '15px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              marginBottom: '20px'
            }}>
              <h2 style={{ color: 'black', fontSize: '18px', marginBottom: '10px' }}>
                System Status
              </h2>
              <p style={{ color: 'green', fontSize: '14px' }}>
                ✅ Frontend: Running on port 8081
              </p>
              <p style={{ color: 'green', fontSize: '14px' }}>
                ✅ Backend: Running on port 5000
              </p>
              <p style={{ color: 'blue', fontSize: '14px' }}>
                🔄 Testing component rendering...
              </p>
            </div>
            <button
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
              onClick={() => alert('Button clicked! React is working.')}
            >
              Test Button
            </button>
          </div>
        ) : (
          <SimpleAssetDataGrid
            onCreateAsset={handleCreateAsset}
            onEditAsset={handleEditAsset}
            onDeleteAsset={handleDeleteAsset}
            onViewFiles={handleViewFiles}
          />
        )}
      </div>
    </AppLayout>
  );
}
