import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Loader2, AlertCircle } from 'lucide-react';
import { Asset } from '@/services/assetService';
import assetService from '@/services/assetService';

interface SafeAssetDataGridProps {
  onCreateAsset: () => void;
  onEditAsset: (asset: Asset) => void;
  onDeleteAsset: (asset: Asset) => void;
  onViewFiles: (asset: Asset) => void;
}

export const SafeAssetDataGrid: React.FC<SafeAssetDataGridProps> = ({
  onCreateAsset,
  onEditAsset,
  onDeleteAsset,
  onViewFiles
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (info: string) => {
    console.log(`SafeAssetDataGrid: ${info}`);
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  useEffect(() => {
    addDebugInfo("Component mounted, starting data load");
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      addDebugInfo("Attempting to load assets...");
      
      const response = await assetService.getAssets({
        page: 1,
        limit: 10,
        search: '',
        assetTypeId: '',
        vendorId: '',
        locationId: '',
        status: ''
      });
      
      addDebugInfo(`API Response received: ${response.success ? 'Success' : 'Failed'}`);
      
      if (response.success) {
        setAssets(response.data.assets || []);
        addDebugInfo(`Loaded ${response.data.assets?.length || 0} assets`);
      } else {
        throw new Error(response.message || 'Failed to load assets');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error loading assets';
      addDebugInfo(`Error: ${errorMsg}`);
      setError(errorMsg);
    } finally {
      setLoading(false);
      addDebugInfo("Data loading completed");
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Assets Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>Loading assets...</span>
          </div>
          <div className="mt-4 text-xs text-gray-500">
            <details>
              <summary>Debug Information</summary>
              <div className="mt-2 bg-gray-900 text-green-400 p-2 rounded font-mono text-xs max-h-32 overflow-y-auto">
                {debugInfo.map((info, index) => (
                  <div key={index}>{info}</div>
                ))}
              </div>
            </details>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Assets Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle className="h-8 w-8 mr-2" />
            <div>
              <p className="font-medium">Failed to load assets</p>
              <p className="text-sm text-gray-600">{error}</p>
            </div>
          </div>
          <div className="mt-4 flex justify-center">
            <Button onClick={loadData} variant="outline">
              Retry
            </Button>
          </div>
          <div className="mt-4 text-xs text-gray-500">
            <details>
              <summary>Debug Information</summary>
              <div className="mt-2 bg-gray-900 text-green-400 p-2 rounded font-mono text-xs max-h-32 overflow-y-auto">
                {debugInfo.map((info, index) => (
                  <div key={index}>{info}</div>
                ))}
              </div>
            </details>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Assets Management</CardTitle>
        <Button onClick={onCreateAsset} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Asset
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {assets.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No assets found</p>
              <Button onClick={onCreateAsset} variant="outline">
                Create your first asset
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {assets.slice(0, 5).map((asset) => (
                <div key={asset.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{asset.name}</h3>
                      <p className="text-sm text-gray-600">{asset.description}</p>
                      <p className="text-xs text-gray-500">Status: {asset.status}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => onEditAsset(asset)}>
                        Edit
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => onViewFiles(asset)}>
                        Files
                      </Button>
                      <Button size="sm" variant="destructive" onClick={() => onDeleteAsset(asset)}>
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              {assets.length > 5 && (
                <p className="text-center text-sm text-gray-500">
                  Showing 5 of {assets.length} assets
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
