import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

// Asset attributes interface
export interface AssetAttributes {
  id: number; // Auto-incrementing primary key
  assetId: string; // Unique asset identifier
  name: string; // Asset name
  description?: string; // Asset description
  serialNumber?: string; // Serial number
  category: string; // Asset category
  location: string; // Location (simple string, not foreign key)
  status: 'available' | 'checked_out' | 'in_maintenance' | 'damaged' | 'retired';
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  purchaseDate?: Date;
  purchasePrice?: number; // Purchase price
  vendorId?: number; // Foreign key to Vendor
  assignedTo?: string; // Personnel or precinct assigned to
  lastMaintenance?: Date;
  nextMaintenance?: Date;
  notes?: string;

  createdAt?: Date;
  updatedAt?: Date;
}

// Optional attributes for creation
export interface AssetCreationAttributes extends Optional<AssetAttributes, 'id' | 'description' | 'serialNumber' | 'purchaseDate' | 'purchasePrice' | 'vendorId' | 'assignedTo' | 'lastMaintenance' | 'nextMaintenance' | 'notes' | 'createdAt' | 'updatedAt'> {}

// Asset model class
class Asset extends Model<AssetAttributes, AssetCreationAttributes> implements AssetAttributes {
  public id!: number;
  public assetId!: string;
  public name!: string;
  public description?: string;
  public serialNumber?: string;
  public category!: string;
  public location!: string;
  public status!: 'available' | 'checked_out' | 'in_maintenance' | 'damaged' | 'retired';
  public condition!: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  public purchaseDate?: Date;
  public purchasePrice?: number;
  public vendorId?: number;
  public assignedTo?: string;
  public lastMaintenance?: Date;
  public nextMaintenance?: Date;
  public notes?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

// Initialize the model
Asset.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    assetId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      field: 'asset_id',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    serialNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'serial_number',
    },
    category: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('available', 'checked_out', 'in_maintenance', 'damaged', 'retired'),
      allowNull: false,
      defaultValue: 'available',
    },
    condition: {
      type: DataTypes.ENUM('excellent', 'good', 'fair', 'poor', 'damaged'),
      allowNull: false,
      defaultValue: 'good',
    },
    purchaseDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'purchase_date',
    },
    purchasePrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'purchase_price',
    },
    vendorId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'vendor_id',
      references: {
        model: 'vendors',
        key: 'id'
      }
    },
    assignedTo: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'assigned_to',
    },
    lastMaintenance: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_maintenance_date',
    },
    nextMaintenance: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'next_maintenance_date',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'Asset',
    tableName: 'assets',
    timestamps: true,
  }
);

export default Asset;