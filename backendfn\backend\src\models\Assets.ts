import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

// Asset attributes interface
export interface AssetAttributes {
  id: number; // Auto-incrementing Asset Tag as primary key
  name: string; // Asset name
  description?: string; // Asset description
  assetTypeId: string; // Foreign key to AssetType
  assetModelId?: string; // Foreign key to AssetModel
  vendorId?: number; // Foreign key to Vendor
  locationId?: string; // Foreign key to Location
  serialNumber?: string; // Dynamic serial number based on category
  status: 'Available' | 'In Transit' | 'Deployed' | 'Damaged' | 'Under Repair' | 'Retired';
  condition: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  assignedTo?: string; // Personnel or precinct assigned to
  county?: string;
  precinct?: string;

  // Dates
  purchaseDate?: Date;
  warrantyExpiry?: Date;
  lastMaintenance?: Date;
  nextMaintenance?: Date;
  lastChecked?: Date;

  // Additional metadata
  notes?: string;
  specifications?: string; // JSON string for technical specs

  createdAt?: Date;
  updatedAt?: Date;
}

// Optional attributes for creation
export interface AssetCreationAttributes extends Optional<AssetAttributes, 'id' | 'description' | 'assetModelId' | 'vendorId' | 'locationId' | 'serialNumber' | 'assignedTo' | 'county' | 'precinct' | 'purchaseDate' | 'warrantyExpiry' | 'lastMaintenance' | 'nextMaintenance' | 'lastChecked' | 'notes' | 'specifications' | 'createdAt' | 'updatedAt'> {}

// Asset model class
class Asset extends Model<AssetAttributes, AssetCreationAttributes> implements AssetAttributes {
  public id!: number;
  public name!: string;
  public description?: string;
  public assetTypeId!: string;
  public assetModelId?: string;
  public vendorId?: number;
  public locationId?: string;
  public serialNumber?: string;
  public status!: 'Available' | 'In Transit' | 'Deployed' | 'Damaged' | 'Under Repair' | 'Retired';
  public condition!: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  public assignedTo?: string;
  public county?: string;
  public precinct?: string;
  public purchaseDate?: Date;
  public warrantyExpiry?: Date;
  public lastMaintenance?: Date;
  public nextMaintenance?: Date;
  public lastChecked?: Date;
  public notes?: string;
  public specifications?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Helper method to get specifications as object
  public getSpecificationsData(): any {
    try {
      return this.specifications ? JSON.parse(this.specifications) : {};
    } catch (error) {
      console.error('Error parsing specifications:', error);
      return {};
    }
  }

  // Helper method to set specifications from object
  public setSpecificationsData(specs: any): void {
    this.specifications = JSON.stringify(specs);
  }
}

// Initialize the model
Asset.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    assetTypeId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'asset_type_id',
      references: {
        model: 'asset_types',
        key: 'id'
      }
    },
    assetModelId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'asset_model_id',
      references: {
        model: 'asset_models',
        key: 'id'
      }
    },
    vendorId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'vendor_id',
      references: {
        model: 'vendors',
        key: 'id'
      }
    },
    locationId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_id',
      references: {
        model: 'masters_locations',
        key: 'id'
      }
    },
    serialNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      field: 'serial_number',
    },
    status: {
      type: DataTypes.ENUM('Available', 'In Transit', 'Deployed', 'Damaged', 'Under Repair', 'Retired'),
      allowNull: false,
      defaultValue: 'Available',
    },
    condition: {
      type: DataTypes.ENUM('Excellent', 'Good', 'Fair', 'Poor', 'Damaged'),
      allowNull: false,
      defaultValue: 'Good',
    },
    assignedTo: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'assigned_to',
    },
    county: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    precinct: {
      type: DataTypes.STRING,
      allowNull: true,
    },    purchaseDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'purchase_date', // Map to actual database column name
    },
    warrantyExpiry: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'warranty_expiry', // Map to actual database column name
    },lastMaintenance: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_maintenance_date', // Map to actual database column name
    },
    nextMaintenance: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'next_maintenance_date', // Map to actual database column name
    },    lastChecked: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_checked', // Map to actual database column name
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    specifications: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'Asset',
    tableName: 'assets',
    timestamps: true,
  }
);

export default Asset;