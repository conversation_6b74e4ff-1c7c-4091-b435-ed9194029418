import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Upload, X } from 'lucide-react';
import { Asset, AssetFormData, DropdownOption } from '@/services/assetService';
import assetService from '@/services/assetService';

interface AssetFormProps {
  asset?: Asset;
  onSubmit: (data: AssetFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export const AssetForm: React.FC<AssetFormProps> = ({
  asset,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<AssetFormData>({
    name: asset?.name || '',
    description: asset?.description || '',
    assetTypeId: asset?.assetTypeId || '',
    assetModelId: asset?.assetModelId || '',
    vendorId: asset?.vendorId || undefined,
    locationId: asset?.locationId || '',
    serialNumber: asset?.serialNumber || '',
    status: asset?.status || 'Available',
    condition: asset?.condition || 'Good',
    assignedTo: asset?.assignedTo || '',
    county: asset?.county || '',
    precinct: asset?.precinct || '',
    purchaseDate: asset?.purchaseDate || undefined,
    warrantyExpiry: asset?.warrantyExpiry || undefined,
    lastMaintenance: asset?.lastMaintenance || undefined,
    nextMaintenance: asset?.nextMaintenance || undefined,
    lastChecked: asset?.lastChecked || undefined,
    notes: asset?.notes || '',
    specifications: asset?.specifications || ''
  });

  const [dropdownData, setDropdownData] = useState({
    assetTypes: [] as DropdownOption[],
    assetModels: [] as DropdownOption[],
    vendors: [] as DropdownOption[],
    locations: [] as DropdownOption[]
  });

  const [loadingDropdowns, setLoadingDropdowns] = useState(true);
  const [loadingModels, setLoadingModels] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load dropdown data on component mount
  useEffect(() => {
    loadDropdownData();
  }, []);

  // Load asset models when asset type changes
  useEffect(() => {
    if (formData.assetTypeId) {
      loadAssetModels(formData.assetTypeId);
      generateSerialNumber(formData.assetTypeId);
    } else {
      setDropdownData(prev => ({ ...prev, assetModels: [] }));
      setFormData(prev => ({ ...prev, assetModelId: '', serialNumber: '' }));
    }
  }, [formData.assetTypeId]);

  const loadDropdownData = async () => {
    try {
      setLoadingDropdowns(true);
      const [assetTypesRes, vendorsRes, locationsRes] = await Promise.all([
        assetService.getAssetTypes(),
        assetService.getVendors(),
        assetService.getLocations()
      ]);

      setDropdownData(prev => ({
        ...prev,
        assetTypes: assetTypesRes.data || [],
        vendors: vendorsRes.data || [],
        locations: locationsRes.data || []
      }));
    } catch (error) {
      console.error('Error loading dropdown data:', error);
    } finally {
      setLoadingDropdowns(false);
    }
  };

  const loadAssetModels = async (categoryId: string) => {
    try {
      setLoadingModels(true);
      const response = await assetService.getAssetModels(categoryId);
      setDropdownData(prev => ({
        ...prev,
        assetModels: response.data || []
      }));
    } catch (error) {
      console.error('Error loading asset models:', error);
      setDropdownData(prev => ({ ...prev, assetModels: [] }));
    } finally {
      setLoadingModels(false);
    }
  };

  const generateSerialNumber = async (categoryId: string) => {
    if (!asset) { // Only generate for new assets
      try {
        const response = await assetService.getNextSerialNumber(categoryId);
        setFormData(prev => ({
          ...prev,
          serialNumber: response.data?.serialNumber || ''
        }));
      } catch (error) {
        console.error('Error generating serial number:', error);
      }
    }
  };

  const handleInputChange = (field: keyof AssetFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Asset name is required';
    }

    if (!formData.assetTypeId) {
      newErrors.assetTypeId = 'Asset type is required';
    }

    if (!formData.status) {
      newErrors.status = 'Status is required';
    }

    if (!formData.condition) {
      newErrors.condition = 'Condition is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const statusOptions = [
    'Available',
    'In Transit',
    'Deployed',
    'Damaged',
    'Under Repair',
    'Retired'
  ];

  const conditionOptions = [
    'Excellent',
    'Good',
    'Fair',
    'Poor',
    'Damaged'
  ];

  if (loadingDropdowns) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading form data...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{asset ? 'Edit Asset' : 'Create New Asset'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Asset Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter asset name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="assetTypeId">Asset Type *</Label>
              <Select
                value={formData.assetTypeId}
                onValueChange={(value) => handleInputChange('assetTypeId', value)}
              >
                <SelectTrigger className={errors.assetTypeId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select asset type" />
                </SelectTrigger>
                <SelectContent>
                  {dropdownData.assetTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.assetTypeId && <p className="text-sm text-red-500">{errors.assetTypeId}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="assetModelId">Asset Model</Label>
              <Select
                value={formData.assetModelId || ''}
                onValueChange={(value) => handleInputChange('assetModelId', value || undefined)}
                disabled={loadingModels || !formData.assetTypeId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={
                    loadingModels ? 'Loading models...' : 
                    !formData.assetTypeId ? 'Select asset type first' : 
                    'Select asset model'
                  } />
                </SelectTrigger>
                <SelectContent>
                  {dropdownData.assetModels.map((model) => (
                    <SelectItem key={model.id} value={model.id.toString()}>
                      {model.modelName} - {model.manufacturer}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="serialNumber">Serial Number</Label>
              <Input
                id="serialNumber"
                value={formData.serialNumber}
                onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                placeholder="Auto-generated or enter manually"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter asset description"
              rows={3}
            />
          </div>

          {/* Status and Condition */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.status && <p className="text-sm text-red-500">{errors.status}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="condition">Condition *</Label>
              <Select
                value={formData.condition}
                onValueChange={(value) => handleInputChange('condition', value)}
              >
                <SelectTrigger className={errors.condition ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select condition" />
                </SelectTrigger>
                <SelectContent>
                  {conditionOptions.map((condition) => (
                    <SelectItem key={condition} value={condition}>
                      {condition}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.condition && <p className="text-sm text-red-500">{errors.condition}</p>}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {asset ? 'Update Asset' : 'Create Asset'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
