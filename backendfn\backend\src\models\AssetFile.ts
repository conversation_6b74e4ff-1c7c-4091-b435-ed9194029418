import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

// Asset File attributes interface
export interface AssetFileAttributes {
  id: number;
  assetId: number; // Foreign key to Asset
  fileName: string; // Original file name
  fileType: string; // MIME type
  fileSize: number; // File size in bytes
  fileData: Buffer; // BLOB data
  uploadDate: Date;
  uploadedBy?: string; // User who uploaded the file
  description?: string; // File description
  isActive: boolean; // Soft delete flag
  createdAt?: Date;
  updatedAt?: Date;
}

// Optional attributes for creation
export interface AssetFileCreationAttributes extends Optional<AssetFileAttributes, 'id' | 'uploadDate' | 'uploadedBy' | 'description' | 'isActive' | 'createdAt' | 'updatedAt'> {}

// Asset File model class
class AssetFile extends Model<AssetFileAttributes, AssetFileCreationAttributes> implements AssetFileAttributes {
  public id!: number;
  public assetId!: number;
  public fileName!: string;
  public fileType!: string;
  public fileSize!: number;
  public fileData!: Buffer;
  public uploadDate!: Date;
  public uploadedBy?: string;
  public description?: string;
  public isActive!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Helper method to get file extension
  public getFileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  // Helper method to check if file is an image
  public isImage(): boolean {
    const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    return imageTypes.includes(this.fileType.toLowerCase());
  }

  // Helper method to check if file is a PDF
  public isPDF(): boolean {
    return this.fileType.toLowerCase() === 'application/pdf';
  }

  // Helper method to get human-readable file size
  public getFormattedFileSize(): string {
    const bytes = this.fileSize;
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Initialize the model
AssetFile.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    assetId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'asset_id',
      references: {
        model: 'assets',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    fileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'file_name',
      validate: {
        notEmpty: true,
      },
    },
    fileType: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'file_type',
      validate: {
        notEmpty: true,
        isIn: [['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']]
      },
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
      validate: {
        min: 1,
        max: 10485760 // 10MB limit
      },
    },
    fileData: {
      type: DataTypes.BLOB('long'),
      allowNull: false,
      field: 'file_data',
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'upload_date',
    },
    uploadedBy: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'uploaded_by',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
    },
  },
  {
    sequelize,
    modelName: 'AssetFile',
    tableName: 'asset_files',
    timestamps: true,
    indexes: [
      {
        name: 'idx_asset_files_asset_id',
        fields: ['asset_id'],
      },
      {
        name: 'idx_asset_files_file_type',
        fields: ['file_type'],
      },
      {
        name: 'idx_asset_files_upload_date',
        fields: ['upload_date'],
      },
      {
        name: 'idx_asset_files_is_active',
        fields: ['is_active'],
      },
    ],
  }
);

export default AssetFile;
