import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { SafeAssetDataGrid } from "@/components/assets/SafeAssetDataGrid";
import { Asset, AssetFormData } from "@/services/assetService";

type ViewMode = 'list' | 'form' | 'files';

interface AssetPageState {
  viewMode: ViewMode;
  selectedAsset: Asset | null;
  isEditing: boolean;
}

export default function SafeAssetsPage() {
  const [state, setState] = useState<AssetPageState>({
    viewMode: 'list',
    selectedAsset: null,
    isEditing: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (info: string) => {
    console.log(`SafeAssetsPage: ${info}`);
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  useEffect(() => {
    addDebugInfo("SafeAssetsPage component mounted");
  }, []);

  // Handle creating a new asset
  const handleCreateAsset = () => {
    addDebugInfo("Create asset clicked");
    alert("Create Asset functionality - Form would open here");
  };

  // Handle editing an existing asset
  const handleEditAsset = (asset: Asset) => {
    addDebugInfo(`Edit asset clicked: ${asset.name}`);
    alert(`Edit Asset: ${asset.name} - Form would open here`);
  };

  // Handle viewing files for an asset
  const handleViewFiles = (asset: Asset) => {
    addDebugInfo(`View files clicked: ${asset.name}`);
    alert(`View Files for: ${asset.name} - File manager would open here`);
  };

  // Handle deleting an asset
  const handleDeleteAsset = async (asset: Asset) => {
    addDebugInfo(`Delete asset clicked: ${asset.name}`);
    if (!confirm(`Are you sure you want to delete "${asset.name}"? This action cannot be undone.`)) {
      return;
    }
    alert(`Delete Asset: ${asset.name} - Would be deleted from database`);
  };

  const renderContent = () => {
    try {
      addDebugInfo(`Rendering content for viewMode: ${state.viewMode}`);
      
      switch (state.viewMode) {
        case 'form':
          return (
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold mb-4">Asset Form</h2>
              <p>Asset form would be rendered here</p>
              <button 
                onClick={() => setState(prev => ({ ...prev, viewMode: 'list' }))}
                className="mt-4 bg-gray-500 text-white px-4 py-2 rounded"
              >
                Back to List
              </button>
            </div>
          );

        case 'files':
          return (
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold mb-4">File Management</h2>
              <p>File management would be rendered here</p>
              <button 
                onClick={() => setState(prev => ({ ...prev, viewMode: 'list' }))}
                className="mt-4 bg-gray-500 text-white px-4 py-2 rounded"
              >
                Back to List
              </button>
            </div>
          );

        case 'list':
        default:
          addDebugInfo("Rendering SafeAssetDataGrid");
          return (
            <SafeAssetDataGrid
              onCreateAsset={handleCreateAsset}
              onEditAsset={handleEditAsset}
              onDeleteAsset={handleDeleteAsset}
              onViewFiles={handleViewFiles}
            />
          );
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown render error';
      addDebugInfo(`❌ Render error: ${errorMsg}`);
      setError(errorMsg);
      return (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <h3 className="font-bold">Render Error</h3>
          <p>{errorMsg}</p>
        </div>
      );
    }
  };

  return (
    <AppLayout>
      <div className="p-6">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h3 className="font-bold">Page Error</h3>
            <p>{error}</p>
          </div>
        )}
        
        <div className="mb-4 text-xs text-gray-500">
          Debug: {debugInfo.length} log entries | View Mode: {state.viewMode}
        </div>
        
        {renderContent()}
        
        <div className="mt-4 text-xs text-gray-500">
          <details>
            <summary>Debug Information</summary>
            <div className="mt-2 bg-gray-900 text-green-400 p-2 rounded font-mono text-xs max-h-32 overflow-y-auto">
              {debugInfo.map((info, index) => (
                <div key={index}>{info}</div>
              ))}
            </div>
          </details>
        </div>
      </div>
    </AppLayout>
  );
}
