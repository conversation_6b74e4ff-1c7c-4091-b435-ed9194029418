import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AssetDataGrid } from "@/components/assets/AssetDataGrid";
import { AssetForm } from "@/components/assets/AssetForm";
import { FileList } from "@/components/assets/FileList";
import { Asset, AssetFormData } from "@/services/assetService";
import assetService from "@/services/assetService";

type ViewMode = 'list' | 'form' | 'files';

interface AssetPageState {
  viewMode: ViewMode;
  selectedAsset: Asset | null;
  isEditing: boolean;
}

export default function AssetsPage() {
  const [state, setState] = useState<AssetPageState>({
    viewMode: 'list',
    selectedAsset: null,
    isEditing: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (info: string) => {
    console.log(`AssetsPage: ${info}`);
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  useEffect(() => {
    addDebugInfo("AssetsPage component mounted");

    // Test if components can be imported
    try {
      addDebugInfo("Testing component imports...");
      if (AssetDataGrid) addDebugInfo("✅ AssetDataGrid imported");
      if (AssetForm) addDebugInfo("✅ AssetForm imported");
      if (FileList) addDebugInfo("✅ FileList imported");
      if (assetService) addDebugInfo("✅ assetService imported");
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown import error';
      addDebugInfo(`❌ Import error: ${errorMsg}`);
      setError(errorMsg);
    }
  }, []);

  // Handle creating a new asset
  const handleCreateAsset = () => {
    setState({
      viewMode: 'form',
      selectedAsset: null,
      isEditing: false
    });
  };

  // Handle editing an existing asset
  const handleEditAsset = (asset: Asset) => {
    setState({
      viewMode: 'form',
      selectedAsset: asset,
      isEditing: true
    });
  };

  // Handle viewing files for an asset
  const handleViewFiles = (asset: Asset) => {
    setState({
      viewMode: 'files',
      selectedAsset: asset,
      isEditing: false
    });
  };

  // Handle deleting an asset
  const handleDeleteAsset = async (asset: Asset) => {
    if (!confirm(`Are you sure you want to delete "${asset.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);
      await assetService.deleteAsset(asset.id);
      // The data grid will refresh automatically
    } catch (error) {
      console.error('Error deleting asset:', error);
      alert('Failed to delete asset. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission (create or update)
  const handleFormSubmit = async (formData: AssetFormData) => {
    try {
      setLoading(true);

      if (state.isEditing && state.selectedAsset) {
        await assetService.updateAsset(state.selectedAsset.id, formData);
      } else {
        await assetService.createAsset(formData);
      }

      // Return to list view
      setState({
        viewMode: 'list',
        selectedAsset: null,
        isEditing: false
      });
    } catch (error) {
      console.error('Error saving asset:', error);
      throw error; // Let the form handle the error display
    } finally {
      setLoading(false);
    }
  };

  // Handle canceling form or file view
  const handleCancel = () => {
    setState({
      viewMode: 'list',
      selectedAsset: null,
      isEditing: false
    });
  };

  const renderContent = () => {
    try {
      addDebugInfo(`Rendering content for viewMode: ${state.viewMode}`);

      switch (state.viewMode) {
        case 'form':
          addDebugInfo("Rendering AssetForm");
          return (
            <AssetForm
              asset={state.selectedAsset}
              onSubmit={handleFormSubmit}
              onCancel={handleCancel}
              isLoading={loading}
            />
          );

        case 'files':
          addDebugInfo("Rendering FileList");
          return state.selectedAsset ? (
            <FileList
              assetId={state.selectedAsset.id}
              assetName={state.selectedAsset.name}
              onClose={handleCancel}
            />
          ) : null;

        case 'list':
        default:
          addDebugInfo("Rendering AssetDataGrid");
          return (
            <AssetDataGrid
              onCreateAsset={handleCreateAsset}
              onEditAsset={handleEditAsset}
              onDeleteAsset={handleDeleteAsset}
              onViewFiles={handleViewFiles}
            />
          );
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown render error';
      addDebugInfo(`❌ Render error: ${errorMsg}`);
      setError(errorMsg);
      return (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <h3 className="font-bold">Render Error</h3>
          <p>{errorMsg}</p>
        </div>
      );
    }
  };

  return (
    <AppLayout>
      <div className="p-6">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h3 className="font-bold">Error</h3>
            <p>{error}</p>
            <details className="mt-2">
              <summary className="cursor-pointer">Debug Information</summary>
              <div className="mt-2 bg-gray-900 text-green-400 p-2 rounded font-mono text-xs max-h-32 overflow-y-auto">
                {debugInfo.map((info, index) => (
                  <div key={index}>{info}</div>
                ))}
              </div>
            </details>
          </div>
        )}

        {!error && (
          <div className="mb-4 text-xs text-gray-500">
            Debug: {debugInfo.length} log entries
          </div>
        )}

        {renderContent()}
      </div>
    </AppLayout>
  );
}
