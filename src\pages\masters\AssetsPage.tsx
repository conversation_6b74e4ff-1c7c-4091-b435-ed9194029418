import React, { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AssetDataGrid } from "@/components/assets/AssetDataGrid";
import { AssetForm } from "@/components/assets/AssetForm";
import { FileList } from "@/components/assets/FileList";
import { Asset, AssetFormData } from "@/services/assetService";
import assetService from "@/services/assetService";

type ViewMode = 'list' | 'form' | 'files';

interface AssetPageState {
  viewMode: ViewMode;
  selectedAsset: Asset | null;
  isEditing: boolean;
}

export default function AssetsPage() {
  const [state, setState] = useState<AssetPageState>({
    viewMode: 'list',
    selectedAsset: null,
    isEditing: false
  });
  const [loading, setLoading] = useState(false);

  // <PERSON>le creating a new asset
  const handleCreateAsset = () => {
    setState({
      viewMode: 'form',
      selectedAsset: null,
      isEditing: false
    });
  };

  // Handle editing an existing asset
  const handleEditAsset = (asset: Asset) => {
    setState({
      viewMode: 'form',
      selectedAsset: asset,
      isEditing: true
    });
  };

  // Handle viewing files for an asset
  const handleViewFiles = (asset: Asset) => {
    setState({
      viewMode: 'files',
      selectedAsset: asset,
      isEditing: false
    });
  };

  // Handle deleting an asset
  const handleDeleteAsset = async (asset: Asset) => {
    if (!confirm(`Are you sure you want to delete "${asset.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);
      await assetService.deleteAsset(asset.id);
      // The data grid will refresh automatically
    } catch (error) {
      console.error('Error deleting asset:', error);
      alert('Failed to delete asset. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission (create or update)
  const handleFormSubmit = async (formData: AssetFormData) => {
    try {
      setLoading(true);

      if (state.isEditing && state.selectedAsset) {
        await assetService.updateAsset(state.selectedAsset.id, formData);
      } else {
        await assetService.createAsset(formData);
      }

      // Return to list view
      setState({
        viewMode: 'list',
        selectedAsset: null,
        isEditing: false
      });
    } catch (error) {
      console.error('Error saving asset:', error);
      throw error; // Let the form handle the error display
    } finally {
      setLoading(false);
    }
  };

  // Handle canceling form or file view
  const handleCancel = () => {
    setState({
      viewMode: 'list',
      selectedAsset: null,
      isEditing: false
    });
  };

  const renderContent = () => {
    switch (state.viewMode) {
      case 'form':
        return (
          <AssetForm
            asset={state.selectedAsset}
            onSubmit={handleFormSubmit}
            onCancel={handleCancel}
            isLoading={loading}
          />
        );

      case 'files':
        return state.selectedAsset ? (
          <FileList
            assetId={state.selectedAsset.id}
            assetName={state.selectedAsset.name}
            onClose={handleCancel}
          />
        ) : null;

      case 'list':
      default:
        return (
          <AssetDataGrid
            onCreateAsset={handleCreateAsset}
            onEditAsset={handleEditAsset}
            onDeleteAsset={handleDeleteAsset}
            onViewFiles={handleViewFiles}
          />
        );
    }
  };

  return (
    <AppLayout>
      <div className="p-6">
        {renderContent()}
      </div>
    </AppLayout>
  );
}
