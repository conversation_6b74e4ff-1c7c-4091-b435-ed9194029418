import React from 'react';

export default function TestPage() {
  return (
    <div style={{ padding: '20px', backgroundColor: 'white', minHeight: '100vh' }}>
      <h1 style={{ color: 'black', fontSize: '24px', marginBottom: '20px' }}>
        Test Page - This should be visible
      </h1>
      <p style={{ color: 'black', fontSize: '16px' }}>
        If you can see this text, the routing and basic React rendering is working.
      </p>
      <div style={{ 
        backgroundColor: '#f0f0f0', 
        padding: '10px', 
        border: '1px solid #ccc',
        marginTop: '20px'
      }}>
        <p style={{ color: 'black' }}>
          Backend Status: Testing connection...
        </p>
      </div>
    </div>
  );
}
