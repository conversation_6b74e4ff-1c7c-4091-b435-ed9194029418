import React from "react";

export default function BasicTestPage() {
  console.log("BasicTestPage component is rendering");
  
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: 'white', 
      color: 'black', 
      fontSize: '18px',
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: 'red', fontSize: '32px', marginBottom: '20px' }}>
        BASIC TEST PAGE - YOU SHOULD SEE THIS
      </h1>
      
      <div style={{ backgroundColor: '#f0f0f0', padding: '15px', marginBottom: '20px' }}>
        <h2 style={{ color: 'blue' }}>Debug Information:</h2>
        <p>✅ React is working</p>
        <p>✅ Component is rendering</p>
        <p>✅ Styles are applied</p>
        <p>✅ Time: {new Date().toLocaleString()}</p>
      </div>
      
      <div style={{ backgroundColor: '#e8f5e8', padding: '15px', border: '2px solid green' }}>
        <h3>If you can see this, the frontend is working!</h3>
        <p>The blank page issue might be with specific components or routing.</p>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => alert('Button clicked! JavaScript is working.')}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          Test JavaScript
        </button>
      </div>
    </div>
  );
}
