import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus } from 'lucide-react';

interface SimpleAssetDataGridProps {
  onCreateAsset: () => void;
  onEditAsset: (asset: any) => void;
  onDeleteAsset: (asset: any) => void;
  onViewFiles: (asset: any) => void;
}

export const SimpleAssetDataGrid: React.FC<SimpleAssetDataGridProps> = ({
  onCreateAsset,
  onEditAsset,
  onDeleteAsset,
  onViewFiles
}) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Assets Management</CardTitle>
        <Button onClick={onCreateAsset} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Asset
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">
              Asset management system is loading...
            </p>
            <p className="text-sm text-gray-400">
              This is a simplified version to test component rendering.
            </p>
          </div>
          
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">Test Asset</h3>
            <p className="text-sm text-gray-600 mb-3">Sample asset for testing</p>
            <div className="flex gap-2">
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => onEditAsset({ id: 1, name: 'Test Asset' })}
              >
                Edit
              </Button>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => onViewFiles({ id: 1, name: 'Test Asset' })}
              >
                Files
              </Button>
              <Button 
                size="sm" 
                variant="destructive"
                onClick={() => onDeleteAsset({ id: 1, name: 'Test Asset' })}
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
