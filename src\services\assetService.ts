// Asset Service - Frontend API service for asset management
const API_BASE_URL = process.env.REACT_APP_API_URL || process.env.VITE_API_URL || 'http://localhost:5000/api';

export interface Asset {
  id: number;
  assetId: string;
  name: string;
  description?: string;
  serialNumber?: string;
  category: string;
  location: string;
  status: 'available' | 'checked_out' | 'in_maintenance' | 'damaged' | 'retired';
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  purchaseDate?: Date;
  purchasePrice?: number;
  vendorId?: number;
  assignedTo?: string;
  lastMaintenance?: Date;
  nextMaintenance?: Date;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // Associated data
  vendor?: {
    id: number;
    companyName: string;
  };
}

export interface DropdownOption {
  id: string | number;
  name: string;
  [key: string]: any;
}

export interface AssetFile {
  id: number;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
  uploadedBy?: string;
  description?: string;
}

export interface AssetFilters {
  status?: string;
  condition?: string;
  category?: string;
  location?: string;
  vendorId?: number;
  assignedTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    assets: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface AssetFormData {
  assetId?: string;
  name: string;
  description?: string;
  serialNumber?: string;
  category: string;
  location: string;
  status: string;
  condition: string;
  purchaseDate?: Date;
  purchasePrice?: number;
  vendorId?: number;
  assignedTo?: string;
  lastMaintenance?: Date;
  nextMaintenance?: Date;
  notes?: string;
}

class AssetService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  private getFileUploadHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData, let browser set it with boundary
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/assets${endpoint}`;

    const defaultOptions: RequestInit = {
      headers: this.getAuthHeaders()
    };

    const response = await fetch(url, {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    });

    return this.handleResponse<T>(response);
  }

  // Get all assets with optional filtering
  async getAssets(filters: AssetFilters = {}): Promise<PaginatedResponse<Asset>> {
    const queryParams = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.makeRequest<PaginatedResponse<Asset>>(endpoint);
  }

  // Get asset by ID
  async getAssetById(id: number): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}`);
  }

  // Create new asset
  async createAsset(assetData: AssetFormData): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>('', {
      method: 'POST',
      body: JSON.stringify(assetData)
    });
  }

  // Update asset
  async updateAsset(id: number, assetData: Partial<AssetFormData>): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(assetData)
    });
  }

  // Delete asset
  async deleteAsset(id: number): Promise<ApiResponse<void>> {
    return this.makeRequest<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE'
    });
  }

  // Dropdown data methods
  async getCategories(): Promise<string[]> {
    const response = await this.makeRequest<string[]>('/categories/list');
    return response;
  }

  async getVendors(): Promise<ApiResponse<DropdownOption[]>> {
    return this.makeRequest<ApiResponse<DropdownOption[]>>('/dropdown/vendors');
  }

  async getLocations(): Promise<ApiResponse<DropdownOption[]>> {
    return this.makeRequest<ApiResponse<DropdownOption[]>>('/dropdown/locations');
  }

  async getLocationsList(): Promise<string[]> {
    const response = await this.makeRequest<string[]>('/locations/list');
    return response;
  }

  // File management methods
  async getAssetFiles(assetId: number): Promise<ApiResponse<AssetFile[]>> {
    return this.makeRequest<ApiResponse<AssetFile[]>>(`/${assetId}/files`);
  }

  async uploadFiles(assetId: number, files: File[], uploadedBy?: string, descriptions?: string[]): Promise<ApiResponse<AssetFile[]>> {
    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    if (uploadedBy) {
      formData.append('uploadedBy', uploadedBy);
    }

    if (descriptions) {
      descriptions.forEach(desc => {
        formData.append('descriptions', desc);
      });
    }

    const response = await fetch(`${API_BASE_URL}/assets/${assetId}/files`, {
      method: 'POST',
      headers: this.getFileUploadHeaders(),
      body: formData
    });

    return this.handleResponse<ApiResponse<AssetFile[]>>(response);
  }

  async downloadFile(fileId: number): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/assets/files/${fileId}/download`, {
      headers: this.getFileUploadHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.status}`);
    }

    return response.blob();
  }

  async deleteFile(fileId: number): Promise<ApiResponse<void>> {
    return this.makeRequest<ApiResponse<void>>(`/files/${fileId}`, {
      method: 'DELETE'
    });
  }
}

export const assetService = new AssetService();
export default assetService;
