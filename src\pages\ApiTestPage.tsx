import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";

export default function ApiTestPage() {
  const [apiResults, setApiResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testEndpoint = async (name: string, url: string, options: RequestInit = {}) => {
    try {
      setLoading(true);
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });
      
      const data = await response.json();
      
      setApiResults(prev => [...prev, {
        name,
        url,
        status: response.status,
        success: response.ok,
        data: data,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error) {
      setApiResults(prev => [...prev, {
        name,
        url,
        status: 'ERROR',
        success: false,
        data: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setLoading(false);
    }
  };

  const runAllTests = async () => {
    setApiResults([]);
    
    // Test health endpoint
    await testEndpoint('Health Check', 'http://localhost:5000/health');
    
    // Test assets endpoint
    await testEndpoint('Assets (No Auth)', 'http://localhost:5000/api/assets');
    
    // Test assets endpoint with auth
    await testEndpoint('Assets (With Auth)', 'http://localhost:5000/api/assets', {
      headers: { 'Authorization': 'Bearer test-token' }
    });
    
    // Test asset types
    await testEndpoint('Asset Types', 'http://localhost:5000/api/asset-types');
    
    // Test vendors
    await testEndpoint('Vendors', 'http://localhost:5000/api/vendors');
    
    // Test locations
    await testEndpoint('Locations', 'http://localhost:5000/api/masters/locations');
  };

  useEffect(() => {
    runAllTests();
  }, []);

  return (
    <AppLayout>
      <div className="p-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">API Connectivity Test</h1>
            <button
              onClick={runAllTests}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Run Tests'}
            </button>
          </div>
          
          <div className="space-y-4">
            {apiResults.length === 0 ? (
              <p className="text-gray-500">Running API tests...</p>
            ) : (
              apiResults.map((result, index) => (
                <div key={index} className={`border rounded-lg p-4 ${
                  result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{result.name}</h3>
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {result.status}
                      </span>
                      <span className="text-xs text-gray-500">{result.timestamp}</span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{result.url}</p>
                  
                  <details className="text-sm">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                      View Response
                    </summary>
                    <pre className="mt-2 bg-gray-900 text-green-400 p-3 rounded overflow-x-auto text-xs">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                </div>
              ))
            )}
          </div>
          
          {apiResults.length > 0 && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">Summary</h3>
              <p className="text-sm text-blue-700">
                {apiResults.filter(r => r.success).length} of {apiResults.length} tests passed
              </p>
              {apiResults.some(r => !r.success) && (
                <p className="text-sm text-red-700 mt-1">
                  Check failed tests above for debugging information
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
