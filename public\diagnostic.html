<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #ffffff !important;
            color: #000000 !important;
            line-height: 1.6;
        }
        
        .diagnostic-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #333;
            background-color: #f9f9f9;
        }
        
        .success { background-color: #d4edda !important; border-color: #28a745 !important; }
        .warning { background-color: #fff3cd !important; border-color: #ffc107 !important; }
        .error { background-color: #f8d7da !important; border-color: #dc3545 !important; }
        
        h1 { color: #dc3545 !important; font-size: 36px !important; margin-bottom: 20px !important; }
        h2 { color: #007bff !important; font-size: 24px !important; margin-bottom: 15px !important; }
        h3 { color: #28a745 !important; font-size: 18px !important; margin-bottom: 10px !important; }
        
        button {
            background-color: #007bff !important;
            color: white !important;
            padding: 10px 20px !important;
            border: none !important;
            border-radius: 5px !important;
            cursor: pointer !important;
            font-size: 16px !important;
            margin: 5px !important;
        }
        
        button:hover {
            background-color: #0056b3 !important;
        }
        
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            margin: 10px 0;
        }
        
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>🔍 COMPREHENSIVE DIAGNOSTIC PAGE</h1>
    
    <div class="diagnostic-section success">
        <h2>✅ Basic HTML/CSS Test</h2>
        <p>If you can see this green box with proper styling, HTML and CSS are working correctly.</p>
        <p><strong>Current time:</strong> <span id="current-time"></span></p>
    </div>
    
    <div class="diagnostic-section warning">
        <h2>⚠️ JavaScript Test</h2>
        <p>Click the button below to test JavaScript functionality:</p>
        <button onclick="runJavaScriptTest()">Test JavaScript</button>
        <button onclick="runNetworkTest()">Test Network</button>
        <button onclick="runBrowserTest()">Test Browser Info</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div class="diagnostic-section">
        <h2>📊 System Information</h2>
        <div id="system-info">
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Screen Resolution:</strong> <span id="screen-resolution"></span></p>
            <p><strong>Viewport Size:</strong> <span id="viewport-size"></span></p>
            <p><strong>Color Depth:</strong> <span id="color-depth"></span></p>
            <p><strong>Language:</strong> <span id="language"></span></p>
            <p><strong>Platform:</strong> <span id="platform"></span></p>
            <p><strong>Cookies Enabled:</strong> <span id="cookies-enabled"></span></p>
            <p><strong>Local Storage:</strong> <span id="local-storage"></span></p>
        </div>
    </div>
    
    <div class="diagnostic-section">
        <h2>🌐 Network & Server Tests</h2>
        <div class="code">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Protocol:</strong> <span id="protocol"></span></p>
            <p><strong>Host:</strong> <span id="host"></span></p>
            <p><strong>Port:</strong> <span id="port"></span></p>
        </div>
    </div>
    
    <div class="diagnostic-section">
        <h2>📝 Test Results</h2>
        <div id="results">
            <p><em>Click the test buttons above to see results here...</em></p>
        </div>
    </div>
    
    <script>
        console.log('🔍 Diagnostic script loaded successfully');
        
        // Initialize page
        function initializePage() {
            try {
                // Update current time
                document.getElementById('current-time').textContent = new Date().toLocaleString();
                
                // System information
                document.getElementById('user-agent').textContent = navigator.userAgent;
                document.getElementById('screen-resolution').textContent = screen.width + 'x' + screen.height;
                document.getElementById('viewport-size').textContent = window.innerWidth + 'x' + window.innerHeight;
                document.getElementById('color-depth').textContent = screen.colorDepth + ' bits';
                document.getElementById('language').textContent = navigator.language;
                document.getElementById('platform').textContent = navigator.platform;
                document.getElementById('cookies-enabled').textContent = navigator.cookieEnabled ? 'Yes' : 'No';
                
                // Test local storage
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    document.getElementById('local-storage').textContent = 'Available';
                } catch (e) {
                    document.getElementById('local-storage').textContent = 'Not available: ' + e.message;
                }
                
                // URL information
                document.getElementById('current-url').textContent = window.location.href;
                document.getElementById('protocol').textContent = window.location.protocol;
                document.getElementById('host').textContent = window.location.hostname;
                document.getElementById('port').textContent = window.location.port || 'default';
                
                addResult('✅ Page initialization completed successfully', 'success');
                
            } catch (error) {
                addResult('❌ Error during initialization: ' + error.message, 'error');
            }
        }
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'warning';
            
            results.innerHTML += `<div class="${className}" style="margin: 5px 0; padding: 8px; border-radius: 3px;">
                <strong>[${timestamp}]</strong> ${message}
            </div>`;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        function runJavaScriptTest() {
            try {
                addResult('🧪 Running JavaScript tests...', 'info');
                
                // Test basic operations
                const testArray = [1, 2, 3, 4, 5];
                const sum = testArray.reduce((a, b) => a + b, 0);
                addResult(`✅ Array operations: Sum of [1,2,3,4,5] = ${sum}`, 'success');
                
                // Test DOM manipulation
                const testDiv = document.createElement('div');
                testDiv.textContent = 'Test element';
                addResult('✅ DOM manipulation: Element created successfully', 'success');
                
                // Test JSON
                const testObj = { test: 'value', number: 42 };
                const jsonString = JSON.stringify(testObj);
                const parsedObj = JSON.parse(jsonString);
                addResult(`✅ JSON operations: ${jsonString}`, 'success');
                
                // Test async operation
                setTimeout(() => {
                    addResult('✅ Async operations: setTimeout working', 'success');
                }, 100);
                
                addResult('✅ All JavaScript tests passed!', 'success');
                
            } catch (error) {
                addResult('❌ JavaScript test failed: ' + error.message, 'error');
            }
        }
        
        function runNetworkTest() {
            addResult('🌐 Running network tests...', 'info');
            
            // Test fetch API
            fetch('/test.html')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ Network: Fetch API working, test.html accessible', 'success');
                    } else {
                        addResult(`⚠️ Network: Fetch returned status ${response.status}`, 'warning');
                    }
                })
                .catch(error => {
                    addResult('❌ Network: Fetch failed - ' + error.message, 'error');
                });
            
            // Test backend connection
            fetch('http://localhost:5000/health')
                .then(response => response.json())
                .then(data => {
                    addResult('✅ Backend: Health check successful - ' + JSON.stringify(data), 'success');
                })
                .catch(error => {
                    addResult('❌ Backend: Health check failed - ' + error.message, 'error');
                });
        }
        
        function runBrowserTest() {
            addResult('🔍 Running browser compatibility tests...', 'info');
            
            // Test ES6 features
            try {
                const testArrow = () => 'arrow function works';
                const [a, b] = [1, 2];
                const obj = { a, b };
                addResult('✅ ES6 features: Arrow functions, destructuring, object shorthand', 'success');
            } catch (error) {
                addResult('❌ ES6 features not supported: ' + error.message, 'error');
            }
            
            // Test modern APIs
            const apis = [
                { name: 'fetch', available: typeof fetch !== 'undefined' },
                { name: 'Promise', available: typeof Promise !== 'undefined' },
                { name: 'localStorage', available: typeof localStorage !== 'undefined' },
                { name: 'sessionStorage', available: typeof sessionStorage !== 'undefined' },
                { name: 'console', available: typeof console !== 'undefined' },
                { name: 'JSON', available: typeof JSON !== 'undefined' }
            ];
            
            apis.forEach(api => {
                if (api.available) {
                    addResult(`✅ API: ${api.name} is available`, 'success');
                } else {
                    addResult(`❌ API: ${api.name} is not available`, 'error');
                }
            });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '<p><em>Results cleared. Click test buttons to run diagnostics...</em></p>';
        }
        
        // Initialize when page loads
        window.addEventListener('load', initializePage);
        
        // Also run immediately in case load event already fired
        if (document.readyState === 'complete') {
            initializePage();
        }
        
        console.log('🔍 Diagnostic page script setup complete');
    </script>
</body>
</html>
